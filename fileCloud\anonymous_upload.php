<?php
/**
 * 文件网盘系统 - 匿名上传页面
 * 创建时间: 2025-07-03
 */

require_once 'functions.php';

// 处理文件上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    // 验证CSRF令牌
    if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
        jsonResponse(['success' => false, 'message' => '无效的请求'], 400);
    }

    $file = $_FILES['file'];
    $selectedUserIds = $_POST['selected_users'] ?? '';
    $expireDays = (int)($_POST['expire_days'] ?? DEFAULT_EXPIRE_DAYS);

    // 验证是否选择了用户
    if (empty($selectedUserIds)) {
        jsonResponse(['success' => false, 'message' => '请先选择要分享的用户'], 400);
    }

    // 解析用户ID列表
    $userIdArray = array_filter(array_map('intval', explode(',', $selectedUserIds)));
    if (empty($userIdArray)) {
        jsonResponse(['success' => false, 'message' => '用户选择无效'], 400);
    }

    // 验证文件
    if ($file['error'] !== UPLOAD_ERR_OK) {
        jsonResponse(['success' => false, 'message' => '文件上传失败：' . $file['error']], 400);
    }

    if ($file['size'] > MAX_FILE_SIZE) {
        jsonResponse(['success' => false, 'message' => '文件大小超过限制（' . formatFileSize(MAX_FILE_SIZE) . '）'], 400);
    }

    if (!isValidFileType($file['name'])) {
        jsonResponse(['success' => false, 'message' => '不支持的文件类型'], 400);
    }

    // 生成文件信息
    $originalName = $file['name'];
    $storedName = generateSecureFilename($originalName);
    $uploadPath = getUploadPath();
    $fullPath = $uploadPath . $storedName;
    $shareCode = generateShareCode();
    $fileType = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $uploadIp = getClientRealIP(); // 获取上传者IP地址

    // 计算过期时间
    $expirationTime = null;
    if ($expireDays > 0) {
        $expirationTime = date('Y-m-d H:i:s', time() + ($expireDays * 24 * 60 * 60));
    }

    // 移动文件到目标目录
    if (!move_uploaded_file($file['tmp_name'], $fullPath)) {
        jsonResponse(['success' => false, 'message' => '文件保存失败'], 500);
    }

    try {
        // 开始事务
        $pdo->beginTransaction();

        // 保存文件信息到数据库（使用特殊的匿名用户ID：0）
        $stmt = $pdo->prepare("
            INSERT INTO filecloud_info
            (user_id, original_name, stored_name, file_size, file_type, share_code, expiration_time, is_public, file_path, uploadIp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            0, // 匿名用户ID设为0
            $originalName,
            $storedName,
            $file['size'],
            $fileType,
            $shareCode,
            $expirationTime,
            0, // 匿名上传的文件不公开，只分享给指定用户
            $fullPath,
            $uploadIp
        ]);

        $fileId = $pdo->lastInsertId();

        // 自动分享给选定的用户
        $shareStmt = $pdo->prepare("
            INSERT INTO filecloud_share (file_id, from_user_id, to_user_id, share_time) 
            VALUES (?, ?, ?, NOW())
        ");

        $sharedCount = 0;
        foreach ($userIdArray as $userId) {
            if ($shareStmt->execute([$fileId, 0, $userId])) { // from_user_id也设为0表示匿名分享
                $sharedCount++;
            }
        }

        // 提交事务
        $pdo->commit();

        jsonResponse([
            'success' => true, 
            'message' => '文件上传成功，已分享给 ' . $sharedCount . ' 个用户',
            'share_code' => $shareCode,
            'file_id' => $fileId,
            'shared_count' => $sharedCount
        ]);

    } catch (PDOException $e) {
        // 回滚事务
        $pdo->rollBack();
        // 删除已上传的文件
        if (file_exists($fullPath)) {
            unlink($fullPath);
        }
        error_log('匿名上传数据库错误: ' . $e->getMessage());
        jsonResponse(['success' => false, 'message' => '数据库保存失败'], 500);
    }
}

$csrfToken = generateCsrfToken();
$maxFileSize = formatFileSize(MAX_FILE_SIZE);
$allowedTypes = implode(', ', ALLOWED_EXTENSIONS);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h(SITE_TITLE) ?> - 匿名上传</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 3px dashed #dee2e6;
            border-radius: 12px;
            padding: 60px 20px;
            text-align: center;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .upload-area.dragover {
            border-color: #0d6efd;
            background: #e7f3ff;
            transform: scale(1.02);
        }
        
        .upload-area:hover {
            border-color: #0d6efd;
            background: #f0f7ff;
        }
        
        .upload-icon {
            font-size: 4rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }
        
        .file-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .progress-container {
            display: none;
            margin-top: 20px;
        }
        
        .success-message {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin-top: 20px;
            display: none;
        }

        .user-selection-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .selected-users-display {
            background: white;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
            min-height: 50px;
            border: 1px solid #dee2e6;
        }

        .user-tag {
            display: inline-block;
            background: #0d6efd;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 0.875rem;
        }

        .user-tag .remove-user {
            margin-left: 5px;
            cursor: pointer;
            opacity: 0.8;
        }

        .user-tag .remove-user:hover {
            opacity: 1;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #dee2e6;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 8px;
        }

        .step.active .step-number {
            background: #0d6efd;
            color: white;
        }

        .step.completed .step-number {
            background: #28a745;
            color: white;
        }

        .step-arrow {
            margin: 0 10px;
            color: #dee2e6;
        }

        .anonymous-notice {
            background: linear-gradient(135deg, #17a2b8, #20c997);
            color: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="download.php">
                <i class="bi bi-cloud-upload me-2"></i><?= h(SITE_TITLE) ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="download.php">
                            <i class="bi bi-download me-1"></i>分享码下载
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="anonymous_upload.php">
                            <i class="bi bi-cloud-upload me-1"></i>匿名上传
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">
                            <i class="bi bi-box-arrow-in-right me-1"></i>登录
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- 匿名上传说明 -->
                <div class="anonymous-notice">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle-fill me-3" style="font-size: 1.5rem;"></i>
                        <div>
                            <h6 class="mb-1">匿名上传功能</h6>
                            <small>无需登录即可上传文件并分享给指定用户。请先选择要分享的用户，然后上传文件。</small>
                        </div>
                    </div>
                </div>

                <!-- 步骤指示器 -->
                <div class="step-indicator">
                    <div class="step active" id="step1">
                        <div class="step-number">1</div>
                        <span>选择用户</span>
                    </div>
                    <div class="step-arrow">
                        <i class="bi bi-arrow-right"></i>
                    </div>
                    <div class="step" id="step2">
                        <div class="step-number">2</div>
                        <span>上传文件</span>
                    </div>
                    <div class="step-arrow">
                        <i class="bi bi-arrow-right"></i>
                    </div>
                    <div class="step" id="step3">
                        <div class="step-number">3</div>
                        <span>完成分享</span>
                    </div>
                </div>

                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-cloud-upload me-2"></i>匿名文件上传
                        </h4>
                        <small class="text-light">最大文件大小：<?= h($maxFileSize) ?> | 支持格式：<?= h($allowedTypes) ?></small>
                    </div>
                    <div class="card-body p-4">
                        <!-- 用户选择区域 -->
                        <div class="user-selection-section" id="userSelectionSection">
                            <h5 class="mb-3">
                                <i class="bi bi-people me-2"></i>第一步：选择要分享的用户
                            </h5>
                            <div class="mb-3">
                                <label for="shareUserSearch" class="form-label">搜索用户</label>
                                <input type="text" class="form-control" id="shareUserSearch" 
                                       placeholder="输入用户姓名或组织单位名称搜索..." autocomplete="off">
                                <small class="form-text text-muted">至少选择一个用户才能继续上传</small>
                            </div>
                            
                            <div id="searchResults" class="list-group mb-3" style="display: none;">
                                <!-- 搜索结果将通过JavaScript动态添加 -->
                            </div>

                            <div class="d-flex align-items-center mb-3">
                                <div class="form-check me-2">
                                    <input class="form-check-input" type="checkbox" id="selectAllResults">
                                    <label class="form-check-label" for="selectAllResults">全选/取消全选</label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">已选择的用户</label>
                                <div class="selected-users-display" id="selectedUsersDisplay">
                                    <span class="text-muted">尚未选择用户</span>
                                </div>
                            </div>

                            <button type="button" class="btn btn-primary" id="proceedToUpload" disabled>
                                <i class="bi bi-arrow-right me-2"></i>继续上传文件
                            </button>
                        </div>

                        <!-- 文件上传区域 -->
                        <div id="fileUploadSection" style="display: none;">
                            <h5 class="mb-3">
                                <i class="bi bi-cloud-upload me-2"></i>第二步：上传文件
                            </h5>

                            <!-- 拖拽上传区域 -->
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="bi bi-cloud-upload"></i>
                                </div>
                                <h5 class="mb-3">拖拽文件到此处或点击选择文件</h5>
                                <p class="text-muted mb-3">支持单文件上传，最大 <?= h($maxFileSize) ?></p>
                                <input type="file" id="fileInput" class="d-none" accept=".<?= implode(',.', ALLOWED_EXTENSIONS) ?>">
                                <button type="button" class="btn btn-primary btn-lg" onclick="document.getElementById('fileInput').click()">
                                    <i class="bi bi-plus-circle me-2"></i>选择文件
                                </button>
                            </div>

                            <!-- 文件信息和设置 -->
                            <div id="fileInfoSection" style="display: none;">
                                <div class="file-info">
                                    <h6 class="mb-3">
                                        <i class="bi bi-file-earmark me-2"></i>文件信息
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>文件名：</strong>
                                            <span id="fileName" class="text-break"></span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>大小：</strong>
                                            <span id="fileSize"></span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>类型：</strong>
                                            <span id="fileType"></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 上传设置 -->
                                <form id="uploadForm" class="mt-4">
                                    <input type="hidden" name="csrf_token" value="<?= h($csrfToken) ?>">
                                    <input type="hidden" name="selected_users" id="selectedUsersInput">

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="expireDays" class="form-label">
                                                    <i class="bi bi-calendar me-1"></i>分享码有效期
                                                </label>
                                                <select class="form-select" id="expireDays" name="expire_days">
                                                    <option value="0">永不过期</option>
                                                    <option value="1" selected>1天</option>
                                                    <option value="7">7天</option>
                                                    <option value="30">30天</option>
                                                    <option value="90">90天</option>
                                                    <option value="365">1年</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">
                                                    <i class="bi bi-people me-1"></i>分享给用户
                                                </label>
                                                <div class="form-control" style="height: auto; min-height: 38px;">
                                                    <span id="uploadSelectedUsers" class="text-muted">未选择用户</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-success btn-lg flex-grow-1">
                                            <i class="bi bi-upload me-2"></i>开始上传并分享
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-lg" onclick="resetUpload()">
                                            <i class="bi bi-arrow-clockwise me-2"></i>重新选择
                                        </button>
                                        <button type="button" class="btn btn-outline-primary btn-lg" onclick="backToUserSelection()">
                                            <i class="bi bi-arrow-left me-2"></i>返回选择用户
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 上传进度 -->
                        <div class="progress-container">
                            <h6 class="mb-2">
                                <i class="bi bi-upload me-2"></i>上传进度
                            </h6>
                            <div class="progress mb-3" style="height: 12px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%">
                                </div>
                            </div>
                            <div class="text-center">
                                <small class="text-muted" id="uploadStatus">准备上传...</small>
                            </div>
                        </div>

                        <!-- 成功消息 -->
                        <div class="success-message" id="successMessage">
                            <div class="mb-3">
                                <i class="bi bi-check-circle-fill" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="mb-3">文件上传成功！</h5>
                            <div class="mb-3">
                                <strong>分享码：</strong>
                                <code id="shareCodeDisplay" class="fs-4 px-3 py-2 bg-light text-dark rounded"></code>
                                <button class="btn btn-outline-light btn-sm ms-2" onclick="copyShareCodeToClipboard()">
                                    <i class="bi bi-clipboard"></i> 复制
                                </button>
                            </div>
                            <div class="mb-3">
                                <small id="shareInfo" class="text-light"></small>
                            </div>
                            <div class="d-flex gap-2 justify-content-center">
                                <a href="download.php" class="btn btn-light">
                                    <i class="bi bi-download me-1"></i>分享码下载
                                </a>
                                <button class="btn btn-outline-light" onclick="uploadAnother()">
                                    <i class="bi bi-plus-circle me-1"></i>继续上传
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let selectedUsers = new Map(); // 存储选中的用户 {id: {name, organization_unitName}}
        let userSearchResults = [];
        let selectedFile = null;
        let currentShareCode = '';

        // DOM元素
        const userSelectionSection = document.getElementById('userSelectionSection');
        const fileUploadSection = document.getElementById('fileUploadSection');
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfoSection = document.getElementById('fileInfoSection');
        const uploadForm = document.getElementById('uploadForm');
        const progressContainer = document.querySelector('.progress-container');
        const successMessage = document.getElementById('successMessage');
        const proceedToUploadBtn = document.getElementById('proceedToUpload');

        // 步骤指示器更新
        function updateStepIndicator(currentStep) {
            const steps = ['step1', 'step2', 'step3'];
            steps.forEach((stepId, index) => {
                const step = document.getElementById(stepId);
                step.classList.remove('active', 'completed');

                if (index + 1 < currentStep) {
                    step.classList.add('completed');
                } else if (index + 1 === currentStep) {
                    step.classList.add('active');
                }
            });
        }

        // 用户搜索功能
        document.getElementById('shareUserSearch').addEventListener('input', function(e) {
            const keyword = e.target.value.trim();
            if (keyword.length < 1) {
                document.getElementById('searchResults').style.display = 'none';
                return;
            }

            const formData = new FormData();
            formData.append('controlCode', 'query');
            formData.append('search_keyword', keyword);

            fetch('api/user_manage.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultsContainer = document.getElementById('searchResults');
                resultsContainer.innerHTML = '';

                if (data.status === 1 && data.data.length > 0) {
                    userSearchResults = data.data;

                    data.data.forEach(user => {
                        const resultItem = document.createElement('div');
                        resultItem.className = 'list-group-item';
                        resultItem.dataset.userId = user.id;

                        const isSelected = selectedUsers.has(user.id);
                        resultItem.innerHTML = `
                            <div class="form-check d-flex align-items-center">
                                <input type="checkbox" class="form-check-input me-2"
                                       id="user-${user.id}"
                                       ${isSelected ? 'checked' : ''}
                                       onchange="toggleUserSelection(${user.id}, this.checked)">
                                <label class="form-check-label flex-grow-1" for="user-${user.id}">
                                    ${user.name}（${user.organization_unitName}）
                                </label>
                            </div>
                        `;
                        resultsContainer.appendChild(resultItem);
                    });

                    resultsContainer.style.display = 'block';
                } else {
                    const noResult = document.createElement('div');
                    noResult.className = 'list-group-item text-muted';
                    noResult.textContent = '未找到匹配的用户';
                    resultsContainer.appendChild(noResult);
                    resultsContainer.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('搜索用户失败:', error);
                showAlert('搜索用户失败，请稍后重试', 'danger');
            });
        });

        // 切换用户选择
        function toggleUserSelection(userId, isSelected) {
            const user = userSearchResults.find(u => u.id == userId);
            if (!user) return;

            if (isSelected) {
                selectedUsers.set(userId, user);
            } else {
                selectedUsers.delete(userId);
            }

            updateSelectedUsersDisplay();
            updateProceedButton();
        }

        // 更新已选用户显示
        function updateSelectedUsersDisplay() {
            const display = document.getElementById('selectedUsersDisplay');
            const uploadDisplay = document.getElementById('uploadSelectedUsers');

            if (selectedUsers.size === 0) {
                display.innerHTML = '<span class="text-muted">尚未选择用户</span>';
                uploadDisplay.innerHTML = '<span class="text-muted">未选择用户</span>';
            } else {
                const userTags = Array.from(selectedUsers.values()).map(user =>
                    `<span class="user-tag">
                        ${user.name}（${user.organization_unitName}）
                        <span class="remove-user" onclick="removeUser(${user.id})">&times;</span>
                    </span>`
                ).join('');
                display.innerHTML = userTags;

                const userNames = Array.from(selectedUsers.values()).map(user => user.name).join(', ');
                uploadDisplay.textContent = userNames;
            }
        }

        // 移除用户
        function removeUser(userId) {
            selectedUsers.delete(userId);
            updateSelectedUsersDisplay();
            updateProceedButton();

            // 更新搜索结果中的复选框状态
            const checkbox = document.getElementById(`user-${userId}`);
            if (checkbox) {
                checkbox.checked = false;
            }
        }

        // 更新继续按钮状态
        function updateProceedButton() {
            proceedToUploadBtn.disabled = selectedUsers.size === 0;
        }

        // 全选/取消全选
        document.getElementById('selectAllResults').addEventListener('change', function(e) {
            const checkboxes = document.querySelectorAll('#searchResults input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
                const userId = parseInt(checkbox.id.replace('user-', ''));
                toggleUserSelection(userId, e.target.checked);
            });
        });

        // 继续到上传步骤
        proceedToUploadBtn.addEventListener('click', function() {
            if (selectedUsers.size === 0) {
                showAlert('请先选择要分享的用户', 'warning');
                return;
            }

            userSelectionSection.style.display = 'none';
            fileUploadSection.style.display = 'block';
            updateStepIndicator(2);

            // 更新隐藏字段
            const userIds = Array.from(selectedUsers.keys()).join(',');
            document.getElementById('selectedUsersInput').value = userIds;
        });

        // 返回用户选择
        function backToUserSelection() {
            fileUploadSection.style.display = 'none';
            userSelectionSection.style.display = 'block';
            updateStepIndicator(1);
            resetFileSelection();
        }

        // 文件拖拽功能
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        // 文件选择
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        // 处理文件选择
        function handleFileSelect(file) {
            const maxSize = <?= MAX_FILE_SIZE ?>;
            const allowedTypes = <?= json_encode(ALLOWED_EXTENSIONS) ?>;

            // 验证文件大小
            if (file.size > maxSize) {
                showAlert('文件大小超过限制（<?= h($maxFileSize) ?>）', 'danger');
                return;
            }

            // 验证文件类型
            const extension = file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(extension)) {
                showAlert('不支持的文件类型', 'danger');
                return;
            }

            selectedFile = file;

            // 显示文件信息
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileType').textContent = extension.toUpperCase();

            // 显示文件信息区域
            fileInfoSection.style.display = 'block';
            uploadArea.style.display = 'none';
        }

        // 重置文件选择
        function resetFileSelection() {
            selectedFile = null;
            fileInput.value = '';
            uploadArea.style.display = 'block';
            fileInfoSection.style.display = 'none';
        }

        // 表单提交
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            if (!selectedFile) {
                showAlert('请先选择文件', 'danger');
                return;
            }

            if (selectedUsers.size === 0) {
                showAlert('请先选择要分享的用户', 'danger');
                return;
            }

            const formData = new FormData(uploadForm);
            formData.append('file', selectedFile);

            // 显示进度条
            progressContainer.style.display = 'block';
            fileInfoSection.style.display = 'none';
            updateStepIndicator(3);

            try {
                const xhr = new XMLHttpRequest();

                // 上传进度
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        updateProgress(percentComplete);
                    }
                });

                xhr.onload = function() {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            showSuccess(response.share_code, response.shared_count);
                        } else {
                            showAlert(response.message, 'danger');
                            resetToFileSelect();
                        }
                    } else {
                        showAlert('上传失败，请稍后重试', 'danger');
                        resetToFileSelect();
                    }
                };

                xhr.onerror = function() {
                    showAlert('网络错误，请检查网络连接', 'danger');
                    resetToFileSelect();
                };

                xhr.open('POST', 'anonymous_upload.php');
                xhr.send(formData);

            } catch (error) {
                showAlert('上传失败：' + error.message, 'danger');
                resetToFileSelect();
            }
        });

        // 更新进度条
        function updateProgress(percent) {
            const progressBar = document.querySelector('.progress-bar');
            const statusText = document.getElementById('uploadStatus');

            progressBar.style.width = percent + '%';

            if (percent < 100) {
                statusText.textContent = `上传中... ${percent.toFixed(1)}%`;
            } else {
                statusText.textContent = '处理中，请稍候...';
            }
        }

        // 显示成功消息
        function showSuccess(shareCode, sharedCount) {
            currentShareCode = shareCode;
            document.getElementById('shareCodeDisplay').textContent = shareCode;
            document.getElementById('shareInfo').textContent = `文件已成功分享给 ${sharedCount} 个用户`;

            progressContainer.style.display = 'none';
            successMessage.style.display = 'block';
        }

        function resetToFileSelect() {
            progressContainer.style.display = 'none';
            fileInfoSection.style.display = 'block';
            updateStepIndicator(2);
        }

        // 重置上传
        function resetUpload() {
            resetFileSelection();
        }

        // 继续上传
        function uploadAnother() {
            selectedUsers.clear();
            updateSelectedUsersDisplay();
            updateProceedButton();

            userSelectionSection.style.display = 'block';
            fileUploadSection.style.display = 'none';
            progressContainer.style.display = 'none';
            successMessage.style.display = 'none';

            resetFileSelection();
            updateStepIndicator(1);

            // 清空搜索框
            document.getElementById('shareUserSearch').value = '';
            document.getElementById('searchResults').style.display = 'none';
        }

        // 复制分享码
        function copyShareCodeToClipboard() {
            navigator.clipboard.writeText(currentShareCode).then(() => {
                showAlert('分享码已复制到剪贴板', 'success');
            }).catch(() => {
                showAlert('复制失败，请手动复制', 'warning');
            });
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes >= 1073741824) {
                return (bytes / 1073741824).toFixed(2) + ' GB';
            } else if (bytes >= 1048576) {
                return (bytes / 1048576).toFixed(2) + ' MB';
            } else if (bytes >= 1024) {
                return (bytes / 1024).toFixed(2) + ' KB';
            } else {
                return bytes + ' B';
            }
        }

        // 显示提示消息
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);

            setTimeout(() => {
                const alert = document.querySelector('.alert:last-child');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 5000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStepIndicator(1);
        });
    </script>
</body>
</html>
