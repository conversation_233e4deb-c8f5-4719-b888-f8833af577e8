<?php
/**
 * 文件网盘系统 - 文件上传页面
 * 创建时间: 2025-06-23
 */

require_once 'functions.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    redirect('login.php');
}

$currentUserId = $_SESSION['user_id'];
$currentUsername = $_SESSION['username'] ?? '用户';

// 处理文件上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
        jsonResponse(['success' => false, 'message' => '无效的请求'], 400);
    }

    $file = $_FILES['file'];
    $shareByUser = isset($_POST['share_by_user']) && $_POST['share_by_user'] === '1';
    $selectedUserIds = $_POST['selected_users'] ?? '';
    $expireDays = (int)($_POST['expire_days'] ?? DEFAULT_EXPIRE_DAYS);
    $isPublic = isset($_POST['is_public']) ? 1 : 0;

    // 如果选择按用户分享，验证是否选择了用户
    if ($shareByUser) {
        if (empty($selectedUserIds)) {
            jsonResponse(['success' => false, 'message' => '请先选择要分享的用户'], 400);
        }

        // 解析用户ID列表
        $userIdArray = array_filter(array_map('intval', explode(',', $selectedUserIds)));
        if (empty($userIdArray)) {
            jsonResponse(['success' => false, 'message' => '用户选择无效'], 400);
        }
    } else {
        $userIdArray = [];
    }

    // 验证文件
    if ($file['error'] !== UPLOAD_ERR_OK) {
        jsonResponse(['success' => false, 'message' => '文件上传失败：' . $file['error']], 400);
    }

    if ($file['size'] > MAX_FILE_SIZE) {
        jsonResponse(['success' => false, 'message' => '文件大小超过限制（' . formatFileSize(MAX_FILE_SIZE) . '）'], 400);
    }

    if (!isValidFileType($file['name'])) {
        jsonResponse(['success' => false, 'message' => '不支持的文件类型'], 400);
    }

    // 生成文件信息
    $originalName = $file['name'];
    $storedName = generateSecureFilename($originalName);
    $uploadPath = getUploadPath();
    $fullPath = $uploadPath . $storedName;
    $shareCode = generateShareCode();
    $fileType = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $uploadIp = getClientRealIP(); // 获取上传者IP地址

    // 计算过期时间
    $expirationTime = null;
    if ($expireDays > 0) {
        $expirationTime = date('Y-m-d H:i:s', time() + ($expireDays * 24 * 60 * 60));
    }

    // 移动文件到目标目录
    if (!move_uploaded_file($file['tmp_name'], $fullPath)) {
        jsonResponse(['success' => false, 'message' => '文件保存失败'], 500);
    }

    // 保存文件信息到数据库
    try {
        // 开始事务
        $pdo->beginTransaction();

        $stmt = $pdo->prepare("
            INSERT INTO filecloud_info
            (user_id, original_name, stored_name, file_size, file_type, share_code, expiration_time, is_public, file_path, uploadIp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $currentUserId,
            $originalName,
            $storedName,
            $file['size'],
            $fileType,
            $shareCode,
            $expirationTime,
            $isPublic,
            $fullPath,
            $uploadIp
        ]);

        $fileId = $pdo->lastInsertId();

        // 如果选择按用户分享，自动分享给选定的用户
        $sharedCount = 0;
        if ($shareByUser && !empty($userIdArray)) {
            $shareStmt = $pdo->prepare("
                INSERT INTO filecloud_share (file_id, from_user_id, to_user_id)
                VALUES (?, ?, ?)
            ");

            foreach ($userIdArray as $userId) {
                if ($shareStmt->execute([$fileId, $currentUserId, $userId])) {
                    $sharedCount++;
                }
            }
        }

        // 获取分享对象信息
        $sharedUsers = [];
        if ($shareByUser && !empty($userIdArray)) {
            $userStmt = $pdo->prepare("
                SELECT u.name, ut.unit_name as organization_unitName
                FROM 3_user u
                LEFT JOIN 2_unit ut ON u.organization_unit = ut.id
                WHERE u.id IN (" . implode(',', array_fill(0, count($userIdArray), '?')) . ")
            ");
            $userStmt->execute($userIdArray);
            $sharedUsers = $userStmt->fetchAll(PDO::FETCH_ASSOC);
        }

        // 提交事务
        $pdo->commit();

        // 构建响应数据
        $responseData = [
            'success' => true,
            'message' => '文件上传成功',
            'file_info' => [
                'file_name' => $originalName,
                'file_size' => $file['size'],
                'file_type' => $fileType,
                'share_code' => $shareCode,
                'expire_days' => $expireDays,
                'upload_time' => date('Y-m-d H:i:s'),
                'uploader' => $currentUsername,
                'share_by_user' => $shareByUser,
                'shared_users' => $sharedUsers,
                'shared_count' => $sharedCount,
                'is_public' => $isPublic
            ]
        ];

        jsonResponse($responseData);

    } catch (PDOException $e) {
        // 回滚事务（如果有活动事务）
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        // 删除已上传的文件
        if (file_exists($fullPath)) {
            unlink($fullPath);
        }
        error_log('上传数据库错误: ' . $e->getMessage() . ' | 文件: ' . $e->getFile() . ' | 行号: ' . $e->getLine());
        jsonResponse(['success' => false, 'message' => '数据库保存失败: ' . $e->getMessage()], 500);
    }
}

$csrfToken = generateCsrfToken();
$maxFileSize = formatFileSize(MAX_FILE_SIZE);
$allowedTypes = implode(', ', ALLOWED_EXTENSIONS);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h(SITE_TITLE) ?> - 上传文件</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 3px dashed #dee2e6;
            border-radius: 12px;
            padding: 60px 20px;
            text-align: center;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .upload-area.dragover {
            border-color: #0d6efd;
            background: #e7f3ff;
            transform: scale(1.02);
        }
        
        .upload-area:hover {
            border-color: #0d6efd;
            background: #f0f7ff;
        }
        
        .upload-icon {
            font-size: 4rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }
        
        .file-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .progress-container {
            display: none;
            margin-top: 20px;
        }
        
        .success-message {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin-top: 20px;
            display: none;
        }

        .user-selection-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .selected-users-display {
            background: white;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
            min-height: 50px;
            border: 1px solid #dee2e6;
        }

        .user-tag {
            display: inline-block;
            background: #0d6efd;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 0.875rem;
        }

        .user-tag .remove-user {
            margin-left: 5px;
            cursor: pointer;
            opacity: 0.8;
        }

        .user-tag .remove-user:hover {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="bi bi-cloud-upload me-2"></i><?= h(SITE_TITLE) ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-files me-1"></i>我的文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="upload.php">
                            <i class="bi bi-cloud-upload me-1"></i>上传文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="shared.php">
                            <i class="bi bi-share me-1"></i>与我共享
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="download.php">
                            <i class="bi bi-download me-1"></i>分享码下载
                        </a>
                    </li>
                    <?php if (isAdmin()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="admin.php">
                            <i class="bi bi-gear me-1"></i>系统管理
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?= h($currentUsername) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-cloud-upload me-2"></i>上传文件
                        </h4>
                        <small class="text-light">最大文件大小：<?= h($maxFileSize) ?> | 支持格式：<?= h($allowedTypes) ?></small>
                    </div>
                    <div class="card-body p-4">
                        <!-- 拖拽上传区域 -->
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="bi bi-cloud-upload"></i>
                            </div>
                            <h5 class="mb-3">拖拽文件到此处或点击选择文件</h5>
                            <p class="text-muted mb-3">支持单文件上传，最大 <?= h($maxFileSize) ?></p>
                            <input type="file" id="fileInput" class="d-none" accept=".<?= implode(',.', ALLOWED_EXTENSIONS) ?>">
                            <button type="button" class="btn btn-primary btn-lg" onclick="document.getElementById('fileInput').click()">
                                <i class="bi bi-plus-circle me-2"></i>选择文件
                            </button>
                        </div>

                        <!-- 文件信息和设置 -->
                        <div id="fileInfoSection" style="display: none;">
                            <div class="file-info">
                                <h6 class="mb-3">
                                    <i class="bi bi-file-earmark me-2"></i>文件信息
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>文件名：</strong>
                                        <span id="fileName" class="text-break"></span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>大小：</strong>
                                        <span id="fileSize"></span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>类型：</strong>
                                        <span id="fileType"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- 上传设置 -->
                            <form id="uploadForm" class="mt-4">
                                <input type="hidden" name="csrf_token" value="<?= h($csrfToken) ?>">
                                <input type="hidden" name="selected_users" id="selectedUsersInput">

                                <!-- 分享方式选择 -->
                                <div class="mb-4">
                                    <h6 class="mb-3">
                                        <i class="bi bi-share me-2"></i>分享方式
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="shareByUser" name="share_by_user" value="1">
                                                <label class="form-check-label" for="shareByUser">
                                                    <i class="bi bi-people me-1"></i>按用户名分享
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="isPublic" name="is_public">
                                                <label class="form-check-label" for="isPublic">
                                                    <i class="bi bi-key me-1"></i>允许通过分享码公开下载
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 用户选择区域 -->
                                <div class="user-selection-section" id="userSelectionSection" style="display: none;">
                                    <h6 class="mb-3">
                                        <i class="bi bi-people me-2"></i>选择分享用户
                                    </h6>
                                    <div class="mb-3">
                                        <label for="shareUserSearch" class="form-label">搜索用户</label>
                                        <input type="text" class="form-control" id="shareUserSearch"
                                               placeholder="输入用户姓名或组织单位名称搜索..." autocomplete="off">
                                        <small class="form-text text-muted">搜索并选择要分享的用户</small>
                                    </div>

                                    <div id="searchResults" class="list-group mb-3" style="display: none;">
                                        <!-- 搜索结果将通过JavaScript动态添加 -->
                                    </div>

                                    <div class="d-flex align-items-center mb-3">
                                        <div class="form-check me-2">
                                            <input class="form-check-input" type="checkbox" id="selectAllResults">
                                            <label class="form-check-label" for="selectAllResults">全选/取消全选</label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">已选择的用户</label>
                                        <div class="selected-users-display" id="selectedUsersDisplay">
                                            <span class="text-muted">尚未选择用户</span>
                                        </div>
                                        <button type="button" class="btn btn-outline-danger btn-sm mt-2" id="clearAllUsers" style="display: none;">
                                            <i class="bi bi-trash me-1"></i>清除全部
                                        </button>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="expireDays" class="form-label">
                                                <i class="bi bi-calendar me-1"></i>分享时长
                                            </label>
                                            <select class="form-select" id="expireDays" name="expire_days">
                                                <option value="1">1天</option>
                                                <option value="7">7天</option>
                                                <option value="30" selected>30天</option>
                                                <option value="90">90天</option>
                                                <option value="180">180天</option>
                                                <option value="365">1年</option>
                                                <option value="0">永不过期</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-success btn-lg flex-grow-1">
                                        <i class="bi bi-upload me-2"></i>开始上传
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-lg" onclick="resetUpload()">
                                        <i class="bi bi-arrow-clockwise me-2"></i>重新选择
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- 上传进度 -->
                        <div class="progress-container">
                            <h6 class="mb-2">
                                <i class="bi bi-upload me-2"></i>上传进度
                            </h6>
                            <div class="progress mb-3" style="height: 12px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%">
                                </div>
                            </div>
                            <div class="text-center">
                                <small class="text-muted" id="uploadStatus">准备上传...</small>
                            </div>
                        </div>

                        <!-- 成功消息 -->
                        <div class="success-message" id="successMessage">
                            <div class="mb-3">
                                <i class="bi bi-check-circle-fill" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="mb-3">文件上传成功！</h5>
                            <div class="mb-3">
                                <strong>分享码：</strong>
                                <code id="shareCodeDisplay" class="fs-4 px-3 py-2 bg-light text-dark rounded"></code>
                                <button class="btn btn-outline-light btn-sm ms-2" onclick="copyShareCodeToClipboard()">
                                    <i class="bi bi-clipboard"></i> 复制
                                </button>
                            </div>
                            <div class="d-flex gap-2 justify-content-center">
                                <a href="index.php" class="btn btn-light">
                                    <i class="bi bi-files me-1"></i>查看我的文件
                                </a>
                                <button class="btn btn-outline-light" onclick="uploadAnother()">
                                    <i class="bi bi-plus-circle me-1"></i>继续上传
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfoSection = document.getElementById('fileInfoSection');
        const uploadForm = document.getElementById('uploadForm');
        const progressContainer = document.querySelector('.progress-container');
        const successMessage = document.getElementById('successMessage');
        
        let selectedFile = null;
        let currentShareCode = '';

        // 拖拽功能
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        // 文件选择
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        // 处理文件选择
        function handleFileSelect(file) {
            const maxSize = <?= MAX_FILE_SIZE ?>;
            const allowedTypes = <?= json_encode(ALLOWED_EXTENSIONS) ?>;
            
            // 验证文件大小
            if (file.size > maxSize) {
                showAlert('文件大小超过限制（<?= h($maxFileSize) ?>）', 'danger');
                return;
            }
            
            // 验证文件类型
            const extension = file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(extension)) {
                showAlert('不支持的文件类型', 'danger');
                return;
            }
            
            selectedFile = file;
            
            // 显示文件信息
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileType').textContent = extension.toUpperCase();
            
            // 显示文件信息区域
            fileInfoSection.style.display = 'block';
            uploadArea.style.display = 'none';
        }

        // 表单提交
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (!selectedFile) {
                showAlert('请先选择文件', 'danger');
                return;
            }
            
            const formData = new FormData(uploadForm);
            formData.append('file', selectedFile);
            
            // 显示进度条
            progressContainer.style.display = 'block';
            fileInfoSection.style.display = 'none';
            
            try {
                const xhr = new XMLHttpRequest();
                
                // 上传进度
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        updateProgress(percentComplete);
                    }
                });
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            showSuccess(response.share_code);
                        } else {
                            showAlert(response.message, 'danger');
                            resetToFileSelect();
                        }
                    } else {
                        showAlert('上传失败，请稍后重试', 'danger');
                        resetToFileSelect();
                    }
                };
                
                xhr.onerror = function() {
                    showAlert('网络错误，请检查网络连接', 'danger');
                    resetToFileSelect();
                };
                
                xhr.open('POST', 'upload.php');
                xhr.send(formData);
                
            } catch (error) {
                showAlert('上传失败：' + error.message, 'danger');
                resetToFileSelect();
            }
        });

        // 更新进度条
        function updateProgress(percent) {
            const progressBar = document.querySelector('.progress-bar');
            const statusText = document.getElementById('uploadStatus');
            
            progressBar.style.width = percent + '%';
            
            if (percent < 100) {
                statusText.textContent = `上传中... ${percent.toFixed(1)}%`;
            } else {
                statusText.textContent = '处理中，请稍候...';
            }
        }

        // 显示成功消息
        function showSuccess(shareCode) {
            currentShareCode = shareCode;
            document.getElementById('shareCodeDisplay').textContent = shareCode;
            
            progressContainer.style.display = 'none';
            successMessage.style.display = 'block';
        }

        // 重置上传
        function resetUpload() {
            selectedFile = null;
            fileInput.value = '';
            uploadArea.style.display = 'block';
            fileInfoSection.style.display = 'none';
            progressContainer.style.display = 'none';
            successMessage.style.display = 'none';
        }

        function resetToFileSelect() {
            progressContainer.style.display = 'none';
            fileInfoSection.style.display = 'block';
        }

        // 继续上传
        function uploadAnother() {
            resetUpload();
        }

        // 复制分享码
        function copyShareCodeToClipboard() {
            navigator.clipboard.writeText(currentShareCode).then(() => {
                showAlert('分享码已复制到剪贴板', 'success');
            }).catch(() => {
                showAlert('复制失败，请手动复制', 'warning');
            });
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes >= 1073741824) {
                return (bytes / 1073741824).toFixed(2) + ' GB';
            } else if (bytes >= 1048576) {
                return (bytes / 1048576).toFixed(2) + ' MB';
            } else if (bytes >= 1024) {
                return (bytes / 1024).toFixed(2) + ' KB';
            } else {
                return bytes + ' B';
            }
        }

        // 显示提示消息
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);
            
            setTimeout(() => {
                const alert = document.querySelector('.alert:last-child');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 5000);
        }
    </script>
</body>
</html>
