<?php
/**
 * 文件网盘系统 - 文件信息API
 * 创建时间: 2025-06-23
 * 更新时间: 2025-07-15
 */

require_once '../functions.php';

header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => '请先登录'], 401);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => '无效的请求方法'], 405);
}

$fileId = (int)($_GET['file_id'] ?? 0);

if ($fileId <= 0) {
    jsonResponse(['success' => false, 'message' => '参数错误'], 400);
}

try {
    $stmt = $pdo->prepare("
        SELECT f.*, u.username as uploader_name
        FROM filecloud_info f
        LEFT JOIN users u ON f.user_id = u.user_id
        WHERE f.file_id = ? AND f.is_deleted = 0
    ");
    $stmt->execute([$fileId]);
    $file = $stmt->fetch();
    
    if (!$file) {
        jsonResponse(['success' => false, 'message' => '文件不存在'], 404);
    }
    
    // 获取文件的分享用户
    $sharedUsers = [];
    $currentUserId = $_SESSION['user_id'];
    
    // 只有文件所有者可以查看分享用户
    if ($file['user_id'] == $currentUserId) {
        $shareStmt = $pdo->prepare("
            SELECT s.share_id, s.to_user_id, u.username, u.name, ou.name as organization_unitName
            FROM filecloud_share s
            LEFT JOIN users u ON s.to_user_id = u.user_id
            LEFT JOIN organization_unit ou ON u.organization_unit_id = ou.organization_unit_id
            WHERE s.file_id = ? AND s.from_user_id = ?
            ORDER BY s.share_time DESC
        ");
        $shareStmt->execute([$fileId, $currentUserId]);
        $sharedUsers = $shareStmt->fetchAll();
    }
    
    // 格式化文件信息
    $fileInfo = [
        'file_id' => $file['file_id'],
        'original_name' => $file['original_name'],
        'file_size' => $file['file_size'],
        'file_size_formatted' => formatFileSize($file['file_size']),
        'file_type' => strtoupper($file['file_type']),
        'upload_time' => date('Y-m-d H:i:s', strtotime($file['upload_time'])),
        'share_code' => $file['share_code'],
        'expiration_time' => $file['expiration_time'] ? date('Y-m-d H:i:s', strtotime($file['expiration_time'])) : '永不过期',
        'download_count' => number_format($file['download_count']),
        'uploader_name' => $file['uploader_name'] ?? '未知用户',
        'is_owner' => ($file['user_id'] == $currentUserId),
        'shared_users' => array_map(function($user) {
            return [
                'share_id' => $user['share_id'],
                'user_id' => $user['to_user_id'],
                'username' => $user['username'],
                'name' => $user['name'],
                'organization' => $user['organization_unitName']
            ];
        }, $sharedUsers)
    ];
    
    jsonResponse(['success' => true, 'file' => $fileInfo]);
    
} catch (PDOException $e) {
    jsonResponse(['success' => false, 'message' => '获取文件信息失败'], 500);
}
?>